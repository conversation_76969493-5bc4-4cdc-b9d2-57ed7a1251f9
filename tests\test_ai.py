import pytest
import base64
from unittest.mock import patch, MagicMock

from ai_marketplace_monitor.ai import OllamaBackend, OllamaConfig
from ai_marketplace_monitor.facebook import FacebookItemConfig, FacebookMarketplaceConfig
from ai_marketplace_monitor.listing import Listing


@pytest.mark.skipif(True, reason="Condition met, skipping this test")
def test_ai(
    ollama_config: OllamaConfig,
    item_config: FacebookItemConfig,
    marketplace_config: FacebookMarketplaceConfig,
    listing: Listing,
) -> None:
    ai = OllamaBackend(ollama_config)
    # ai.config = ollama_config
    res = ai.evaluate(listing, item_config, marketplace_config)
    assert res.score >= 1 and res.score <= 5


def test_prompt(
    ollama: OllamaBackend,
    listing: Listing,
    item_config: FacebookItemConfig,
    marketplace_config: FacebookMarketplaceConfig,
) -> None:
    prompt = ollama.get_prompt(listing, item_config, marketplace_config)
    assert item_config.name in prompt
    assert (item_config.description or "something weird") in prompt
    assert str(item_config.min_price) in prompt
    assert str(item_config.max_price) in prompt

    assert listing.title in prompt
    assert listing.condition in prompt
    assert listing.price in prompt
    assert listing.post_url in prompt


def test_extra_prompt(
    ollama: OllamaBackend,
    listing: Listing,
    item_config: FacebookItemConfig,
    marketplace_config: FacebookMarketplaceConfig,
) -> None:

    marketplace_config.extra_prompt = "This is an extra prompt"
    prompt = ollama.get_prompt(listing, item_config, marketplace_config)
    assert "extra prompt" in prompt
    #
    item_config.extra_prompt = "This overrides marketplace prompt"
    prompt = ollama.get_prompt(listing, item_config, marketplace_config)
    assert "extra prompt" not in prompt
    assert "overrides marketplace prompt" in prompt
    #
    assert "Great deal: Fully matches" in prompt
    item_config.rating_prompt = "something else"
    prompt = ollama.get_prompt(listing, item_config, marketplace_config)
    assert "Great deal: Fully matches" not in prompt
    assert "something else" in prompt
    #
    assert "Evaluate how well this listing" in prompt
    marketplace_config.prompt = "myprompt"
    prompt = ollama.get_prompt(listing, item_config, marketplace_config)
    assert "Evaluate how well this listing" not in prompt
    assert "myprompt" in prompt


def test_ollama_image_download_and_encoding(
    ollama: OllamaBackend,
    listing: Listing,
    item_config: FacebookItemConfig,
    marketplace_config: FacebookMarketplaceConfig,
) -> None:
    # create a listing with an image URL
    listing_with_image = Listing(
        marketplace=listing.marketplace,
        name=listing.name,
        id=listing.id,
        title=listing.title,
        image="https://example.com/test-image.jpg",
        price=listing.price,
        post_url=listing.post_url,
        location=listing.location,
        seller=listing.seller,
        condition=listing.condition,
        description=listing.description,
    )

    # mock the fetch_with_retry function to return fake image data
    fake_image_data = b"fake_image_data_for_testing"
    fake_content_type = "image/jpeg"

    with patch("ai_marketplace_monitor.ai.fetch_with_retry") as mock_fetch:
        with patch("requests.post") as mock_post:
            # setup mocks
            mock_fetch.return_value = (fake_image_data, fake_content_type)

            mock_response = MagicMock()
            mock_response.json.return_value = {
                "response": "Rating 4: This looks like a good match for your criteria."
            }
            mock_post.return_value = mock_response

            # call the evaluate method
            result = ollama.evaluate(listing_with_image, item_config, marketplace_config)

            # verify fetch_with_retry was called with the image URL
            mock_fetch.assert_called_once_with(listing_with_image.image, logger=ollama.logger)

            # verify the API request was made with base64-encoded image
            mock_post.assert_called_once()
            call_args = mock_post.call_args

            # check that the payload contains the images parameter
            payload = call_args[1]["json"]
            assert "images" in payload
            assert len(payload["images"]) == 1

            # verify the image was base64 encoded correctly
            expected_base64 = base64.b64encode(fake_image_data).decode("utf-8")
            assert payload["images"][0] == expected_base64

            # verify the old "image" parameter is not present
            assert "image" not in payload

            # verify other payload parameters
            assert payload["model"] == ollama.config.model or ollama.default_model
            assert payload["stream"] is False
            assert "prompt" in payload


def test_ollama_no_image_handling(
    ollama: OllamaBackend,
    listing: Listing,
    item_config: FacebookItemConfig,
    marketplace_config: FacebookMarketplaceConfig,
) -> None:
    # create a listing without an image
    listing_no_image = Listing(
        marketplace=listing.marketplace,
        name=listing.name,
        id=listing.id,
        title=listing.title,
        image="",  # empty image
        price=listing.price,
        post_url=listing.post_url,
        location=listing.location,
        seller=listing.seller,
        condition=listing.condition,
        description=listing.description,
    )

    with patch("ai_marketplace_monitor.ai.fetch_with_retry") as mock_fetch:
        with patch("requests.post") as mock_post:
            mock_response = MagicMock()
            mock_response.json.return_value = {
                "response": "Rating 3: This is an okay match."
            }
            mock_post.return_value = mock_response

            # call the evaluate method
            result = ollama.evaluate(listing_no_image, item_config, marketplace_config)

            # verify fetch_with_retry was not called since there's no image
            mock_fetch.assert_not_called()

            # verify the API request was made without images parameter
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            payload = call_args[1]["json"]

            # verify no images parameter is present
            assert "images" not in payload
            assert "image" not in payload
