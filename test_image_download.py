#!/usr/bin/env python3
"""
Simple test script to verify image downloading and base64 encoding functionality
"""

import sys
import base64
from unittest.mock import patch, MagicMock

# Add src to path
sys.path.insert(0, 'src')

from ai_marketplace_monitor.ai import OllamaBackend, OllamaConfig
from ai_marketplace_monitor.facebook import FacebookItemConfig, FacebookMarketplaceConfig
from ai_marketplace_monitor.listing import Listing


def test_image_download_functionality():
    """Test that images are downloaded and base64 encoded correctly"""
    
    # Create test configuration
    ollama_config = OllamaConfig(
        name="test_ollama",
        api_key="test_key",
        provider="ollama",
        base_url="http://localhost:11434",
        model="test_model",
        max_retries=1,
    )
    
    # Create test configs
    item_config = FacebookItemConfig(
        name="test_item",
        search_phrases=["test"],
        min_price=10,
        max_price=100,
    )
    
    marketplace_config = FacebookMarketplaceConfig(
        name="test_marketplace",
        search_region=["usa"],
    )
    
    # Create test listing with image
    listing = Listing(
        marketplace="facebook",
        name="test",
        id="123",
        title="Test Item",
        image="https://example.com/test-image.jpg",
        price="$50",
        post_url="https://www.facebook.com/marketplace/item/123",
        location="Test City",
        seller="Test Seller",
        condition="New",
        description="Test description",
    )
    
    # Create backend
    import logging
    logger = logging.getLogger("test")
    backend = OllamaBackend(ollama_config, logger=logger)
    
    # Mock data
    fake_image_data = b"fake_image_data_for_testing"
    fake_content_type = "image/jpeg"
    
    with patch("ai_marketplace_monitor.ai.fetch_with_retry") as mock_fetch:
        with patch("requests.post") as mock_post:
            with patch("ai_marketplace_monitor.ai.AIResponse.from_cache") as mock_cache:
                # Setup mocks
                mock_cache.return_value = None  # No cached result
                mock_fetch.return_value = (fake_image_data, fake_content_type)

                mock_response = MagicMock()
                mock_response.json.return_value = {
                    "response": "Rating 4: This looks like a good match for your criteria."
                }
                mock_response.raise_for_status.return_value = None  # No HTTP errors
                mock_post.return_value = mock_response

                # Call the evaluate method
                try:
                    result = backend.evaluate(listing, item_config, marketplace_config)
                    print("✓ evaluate() method executed successfully")
                except Exception as e:
                    print(f"✗ evaluate() method failed: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
            
            # Verify fetch_with_retry was called
            if mock_fetch.called:
                print("✓ fetch_with_retry was called")
                call_args = mock_fetch.call_args
                if call_args[0][0] == listing.image:
                    print("✓ fetch_with_retry called with correct image URL")
                else:
                    print(f"✗ fetch_with_retry called with wrong URL: {call_args[0][0]}")
                    return False
            else:
                print("✗ fetch_with_retry was not called")
                return False
            
            # Verify API request was made
            if mock_post.called:
                print("✓ API request was made")
                call_args = mock_post.call_args
                payload = call_args[1]["json"]
                
                # Check images parameter
                if "images" in payload:
                    print("✓ 'images' parameter present in payload")
                    if len(payload["images"]) == 1:
                        print("✓ Exactly one image in payload")
                        
                        # Verify base64 encoding
                        expected_base64 = base64.b64encode(fake_image_data).decode("utf-8")
                        if payload["images"][0] == expected_base64:
                            print("✓ Image correctly base64 encoded")
                        else:
                            print("✗ Image not correctly base64 encoded")
                            return False
                    else:
                        print(f"✗ Wrong number of images: {len(payload['images'])}")
                        return False
                else:
                    print("✗ 'images' parameter missing from payload")
                    return False
                
                # Check that old 'image' parameter is not present
                if "image" not in payload:
                    print("✓ Old 'image' parameter correctly removed")
                else:
                    print("✗ Old 'image' parameter still present")
                    return False
                    
            else:
                print("✗ API request was not made")
                return False
    
    print("\n✓ All tests passed! Image downloading and encoding works correctly.")
    return True


def test_no_image_handling():
    """Test that listings without images are handled correctly"""
    
    # Create test configuration
    ollama_config = OllamaConfig(
        name="test_ollama",
        api_key="test_key", 
        provider="ollama",
        base_url="http://localhost:11434",
        model="test_model",
        max_retries=1,
    )
    
    # Create test configs
    item_config = FacebookItemConfig(
        name="test_item",
        search_phrases=["test"],
        min_price=10,
        max_price=100,
    )
    
    marketplace_config = FacebookMarketplaceConfig(
        name="test_marketplace",
        search_region=["usa"],
    )
    
    # Create test listing without image
    listing = Listing(
        marketplace="facebook",
        name="test",
        id="123",
        title="Test Item",
        image="",  # No image
        price="$50",
        post_url="https://www.facebook.com/marketplace/item/123",
        location="Test City",
        seller="Test Seller",
        condition="New",
        description="Test description",
    )
    
    # Create backend
    import logging
    logger = logging.getLogger("test")
    backend = OllamaBackend(ollama_config, logger=logger)
    
    with patch("ai_marketplace_monitor.ai.fetch_with_retry") as mock_fetch:
        with patch("requests.post") as mock_post:
            with patch("ai_marketplace_monitor.ai.AIResponse.from_cache") as mock_cache:
            mock_cache.return_value = None  # No cached result
            mock_response = MagicMock()
            mock_response.json.return_value = {
                "response": "Rating 3: This is an okay match."
            }
            mock_response.raise_for_status.return_value = None  # No HTTP errors
            mock_post.return_value = mock_response
            
            # Call the evaluate method
            try:
                result = backend.evaluate(listing, item_config, marketplace_config)
                print("✓ evaluate() method executed successfully for no-image case")
            except Exception as e:
                print(f"✗ evaluate() method failed for no-image case: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # Verify fetch_with_retry was not called
            if not mock_fetch.called:
                print("✓ fetch_with_retry was not called (no image)")
            else:
                print("✗ fetch_with_retry was called when it shouldn't have been")
                return False
            
            # Verify API request was made without images
            if mock_post.called:
                print("✓ API request was made")
                call_args = mock_post.call_args
                payload = call_args[1]["json"]

                if "images" not in payload and "image" not in payload:
                    print("✓ No image parameters in payload (correct)")
                else:
                    print("✗ Image parameters present when they shouldn't be")
                    return False
            else:
                print("✗ API request was not made")
                print(f"Mock post call count: {mock_post.call_count}")
                print(f"Mock post called: {mock_post.called}")
                return False
    
    print("✓ No-image handling test passed!")
    return True


if __name__ == "__main__":
    print("Testing image download and encoding functionality...\n")
    
    success1 = test_image_download_functionality()
    print("\n" + "="*60 + "\n")
    success2 = test_no_image_handling()
    
    if success1 and success2:
        print("\n🎉 All tests passed! The image download functionality is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)
